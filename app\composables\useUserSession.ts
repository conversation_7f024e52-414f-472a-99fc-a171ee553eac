import { authClient } from '@/utils/auth-client'

// 基于 better-auth 的方法，兼容了 nuxt-auth-utils 的 useUserSession，避免修改太多代码
export function useUserSession() {
  const session = authClient.useSession()
  const { sendUserSignedOut } = useExtensionBridge()

  // 计算登录状态
  const loggedIn = computed(() => !!session.value?.data?.user)

  // 用户信息
  const user = computed(() => session.value?.data?.user)

  // 清除会话（登出）
  const clear = async () => {
    const currentUser = user.value
    await authClient.signOut()

    // 手动发送登出事件到扩展（作为备用，主要还是依赖服务端的 hooks）
    if (currentUser?.id) {
      sendUserSignedOut()
    }
  }

  return {
    // 会话状态
    loggedIn: readonly(loggedIn),
    user: readonly(user),

    // 方法 - 直接使用 session 对象的方法
    fetch: (session as any).refetch,
    clear,

    // 原始会话对象
    session
  }
}