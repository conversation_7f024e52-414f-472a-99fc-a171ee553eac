/**
 * 扩展通信桥接 Composable
 * 用于在客户端监听认证事件并向浏览器扩展发送消息
 */

interface ExtensionMessage {
  type: 'website_to_ext';
  action: string;
  data?: any;
  timestamp?: string;
}

interface AuthEventData {
  action: 'user_signed_in' | 'user_signed_out' | 'user_updated';
  user?: any;
}

export function useExtensionBridge() {
  const { user } = useUserSession();
  
  /**
   * 向扩展发送消息
   */
  const sendToExtension = (action: string, data?: any) => {
    try {
      const message: ExtensionMessage = {
        type: 'website_to_ext',
        action,
        data,
        timestamp: new Date().toISOString()
      };
      
      window.postMessage(message, '*');
      console.log('Message sent to extension:', message);
    } catch (error) {
      console.error('Failed to send message to extension:', error);
    }
  };
  
  /**
   * 发送用户变更事件
   */
  const sendUserChanged = (userData?: any) => {
    sendToExtension('userChanged', userData || user.value);
  };
  
  /**
   * 发送登录事件
   */
  const sendUserSignedIn = (userData: any) => {
    sendToExtension('userSignedIn', userData);
  };
  
  /**
   * 发送登出事件
   */
  const sendUserSignedOut = () => {
    sendToExtension('userSignedOut');
  };
  
  /**
   * 监听 SSE 消息并转发认证事件到扩展
   */
  const setupSSEListener = () => {
    if (process.client && user.value?.id) {
      try {
        const eventSource = new EventSource(`/api/sse?userId=${user.value.id}`);
        
        eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            
            // 检查是否是认证事件
            if (data.type === 'auth_event') {
              const authData = data.data as AuthEventData;
              
              switch (authData.action) {
                case 'user_signed_in':
                  sendUserSignedIn(authData.user);
                  break;
                case 'user_signed_out':
                  sendUserSignedOut();
                  break;
                case 'user_updated':
                  sendUserChanged(authData.user);
                  break;
              }
            }
          } catch (error) {
            console.error('Failed to parse SSE message:', error);
          }
        };
        
        eventSource.onerror = (error) => {
          console.error('SSE connection error:', error);
        };
        
        // 清理函数
        return () => {
          eventSource.close();
        };
      } catch (error) {
        console.error('Failed to setup SSE listener:', error);
      }
    }
  };
  
  return {
    sendToExtension,
    sendUserChanged,
    sendUserSignedIn,
    sendUserSignedOut,
    setupSSEListener
  };
}
