<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'

const loading = ref(false)

const formSchema = toTypedSchema(
  z.object({
    email: z.string().email('Invalid email'),
    password: z.string().min(6, 'Must be at least 6 characters'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    await $fetch('/api/auth/login-with-password', { method: 'POST', body: values })
    toast('Logged in')

    // 通知扩展用户已登录
    window.postMessage({ type: 'website_to_ext', action: 'userChanged' })

    return navigateTo('/dashboard')
  } catch (error) {
    toast.error(error.data?.statusMessage || error.data?.message || 'An unknown error occurred')
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <form
    class="flex flex-col items-stretch gap-6"
    @submit.prevent="onSubmit"
  >
    <FormField
      v-slot="{ componentField }"
      name="email"
    >
      <FormItem>
        <FormLabel
          class="text-muted-foreground"
          for="email"
        >
          Email
        </FormLabel>
        <FormControl>
          <Input
            v-bind="componentField"
            autocomplete="email"
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField
      v-slot="{ componentField }"
      name="password"
    >
      <FormItem>
        <FormLabel
          class="flex justify-between text-muted-foreground"
          for="password"
        >
          <span>Password</span><NuxtLink
            href="/auth/forgot-password"
            class="text-primary hover:underline"
          >
            Forgot your password?
          </NuxtLink>
        </FormLabel>
        <FormControl>
          <Input
            type="password"
            v-bind="componentField"
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    </FormField>

    <Button
      class="w-full cursor-pointer disabled:opacity-60"
      type="submit"
      size="lg"
      :disabled="!form.meta.value.valid"
    >
      <Icon
        v-if="loading"
        name="svg-spinners:3-dots-fade"
        class="mr-2 size-5"
      />
      <span v-else>Login</span>
    </Button>
  </form>
</template>
