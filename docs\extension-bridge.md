# 扩展通信桥接系统

这个系统使用 better-auth 的 hooks 功能来自动向浏览器扩展发送登录登出消息。

## 架构概述

1. **服务端 Hooks** (`auth.ts`): 使用 better-auth 的 `hooks.after` 来监听认证事件
2. **扩展桥接工具** (`server/utils/extension-bridge.ts`): 服务端工具，通过 SSE 发送消息
3. **客户端桥接** (`app/composables/useExtensionBridge.ts`): 客户端工具，监听 SSE 并转发给扩展
4. **应用集成** (`app.vue`): 在应用中设置 SSE 监听器

## 工作流程

### 登录流程
1. 用户通过 better-auth 登录
2. better-auth 触发 `hooks.after` 中间件
3. 中间件检测到登录事件，调用 `sendSignInEventToExtension()`
4. 服务端通过 SSE 发送消息给客户端
5. 客户端接收 SSE 消息，通过 `postMessage` 发送给扩展

### 登出流程
1. 用户通过 better-auth 登出
2. better-auth 触发 `hooks.after` 中间件
3. 中间件检测到登出事件，调用 `sendSignOutEventToExtension()`
4. 服务端通过 SSE 发送消息给客户端
5. 客户端接收 SSE 消息，通过 `postMessage` 发送给扩展

## 消息格式

### 发送给扩展的消息格式
```typescript
interface ExtensionMessage {
  type: 'website_to_ext';
  action: 'userSignedIn' | 'userSignedOut' | 'userChanged';
  data?: any;
  timestamp?: string;
}
```

### SSE 内部消息格式
```typescript
interface SSEMessage {
  type: 'auth_event';
  action: 'user_signed_in' | 'user_signed_out' | 'user_updated';
  data: {
    action: string;
    user?: any;
  };
  timestamp: string;
}
```

## 使用方法

### 在客户端组件中使用
```vue
<script setup>
const { sendUserChanged, sendUserSignedIn, sendUserSignedOut } = useExtensionBridge()

// 手动发送用户变更事件
const handleUserUpdate = () => {
  sendUserChanged(updatedUserData)
}
</script>
```

### 在服务端 API 中使用
```typescript
import { sendUserUpdateToExtension } from '@@/server/utils/extension-bridge'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  
  // 更新用户数据后
  const updatedUser = await updateUserInDatabase(user.id, newData)
  
  // 通知扩展用户数据已更新
  await sendUserUpdateToExtension(user.id, updatedUser)
  
  return updatedUser
})
```

## 扩展端接收消息

在浏览器扩展的 content script 中：

```javascript
// 监听来自网页的消息
window.addEventListener('message', (event) => {
  if (event.data.type === 'website_to_ext') {
    switch (event.data.action) {
      case 'userSignedIn':
        console.log('用户已登录:', event.data.data)
        // 处理登录逻辑
        break
      case 'userSignedOut':
        console.log('用户已登出')
        // 处理登出逻辑
        break
      case 'userChanged':
        console.log('用户信息已更新:', event.data.data)
        // 处理用户信息更新逻辑
        break
    }
  }
})
```

## 配置说明

### better-auth 配置
在 `auth.ts` 中已经配置了 hooks，会自动监听以下路径：
- `/sign-in/email` - 邮箱登录
- `/sign-in/social` - 社交登录
- `/verify-otp` - OTP 验证
- `/sign-out` - 登出

### SSE 连接
SSE 连接会在 `app.vue` 中自动建立，当用户登录后会开始监听服务端的认证事件。

## 故障排除

1. **SSE 连接失败**: 检查 `/api/sse` 端点是否正常工作
2. **消息未发送到扩展**: 检查浏览器控制台是否有 `postMessage` 相关错误
3. **服务端 hooks 未触发**: 检查 better-auth 配置和路径匹配

## 扩展功能

可以通过以下方式扩展系统：

1. **添加更多事件类型**: 在 `extension-bridge.ts` 中添加新的事件处理函数
2. **自定义消息格式**: 修改接口定义来支持更多数据字段
3. **添加错误处理**: 在各个层级添加更详细的错误处理和重试机制
