<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner'

const route = useRoute()
const appConfig = useAppConfig()
const themeStore = useThemeStore()

const session = authClient.useSession()
const { sendUserChanged, setupSSEListener } = useExtensionBridge()

const { init: initAnalytics } = useAnalytics()
initAnalytics()

useSeoMeta({
  title: computed(() => route.meta.title ? `${appConfig.appName} - ${route.meta.title}` : `${appConfig.appName}`).value as string,
  description: computed(() => route.meta.description ? route.meta.description : `${appConfig.appDescription}`).value as string,
  ogTitle: computed(() => route.meta.title ? `${appConfig.appName} - ${route.meta.title}` : `${appConfig.appName}`).value as string,
  ogDescription: computed(() => route.meta.description ? route.meta.description : `${appConfig.appDescription}`).value as string,
})

// 监听用户登录状态和属性是否变化，并通知扩展
watch(() => session.value.data?.user, (newUser, oldUser) => {
  const isDeepEqual = JSON.stringify(newUser) === JSON.stringify(oldUser);
  if (!isDeepEqual) {
    sendUserChanged(newUser)
  }
},{ deep: true })

onMounted(async () => {
  document.documentElement.style.setProperty('--radius', `${themeStore.radius}rem`)
  document.documentElement.classList.add(`theme-${themeStore.theme}`)

  // 设置 SSE 监听器来接收服务端的认证事件
  const cleanupSSE = setupSSEListener()

  if (session.value.data) {
    // await $fetch('/api/account/refresh')
    sendUserChanged(session.value.data.user)
  }

  // 清理 SSE 连接
  onUnmounted(() => {
    if (cleanupSSE) {
      cleanupSSE()
    }
  })
})
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <NuxtLoadingIndicator
      style="opacity: 1;"
      :height="2"
    />
    <Toaster
      position="top-center"
      :toastOptions="{
        classes: {
          error: '!bg-destructive',
          success: '',
          warning: '',
          info: '',
          toast: '!bg-background !text-foreground',
          title: 'font-semibold',
          description: '',
        },
      }"
    />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <ThemePopoverFixed />
  </TooltipProvider>
</template>
