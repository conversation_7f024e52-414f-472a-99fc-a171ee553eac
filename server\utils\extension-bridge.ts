/**
 * 扩展通信桥接工具
 * 用于在服务端向浏览器扩展发送消息
 */

interface ExtensionMessage {
  type: 'auth_event' | 'user_update' | 'sync_event';
  action: string;
  data?: any;
  timestamp: string;
}

interface AuthEventData {
  action: 'user_signed_in' | 'user_signed_out' | 'user_updated';
  user?: any;
}

/**
 * 向扩展发送认证事件
 */
export async function sendAuthEventToExtension(userId: string, eventData: AuthEventData) {
  try {
    // 动态导入 useSSE 以避免循环依赖
    const { useSSE } = await import('~/composables/useSSE');
    const { sendToUser } = useSSE();
    
    const message: ExtensionMessage = {
      type: 'auth_event',
      action: eventData.action,
      data: eventData,
      timestamp: new Date().toISOString()
    };
    
    // 通过 SSE 发送消息给用户
    sendToUser(userId, JSON.stringify(message));
    
    console.log(`Extension auth event sent: ${eventData.action} for user ${userId}`);
  } catch (error) {
    console.error('Failed to send auth event to extension:', error);
  }
}

/**
 * 向扩展发送用户更新事件
 */
export async function sendUserUpdateToExtension(userId: string, userData: any) {
  await sendAuthEventToExtension(userId, {
    action: 'user_updated',
    user: userData
  });
}

/**
 * 向扩展发送登录事件
 */
export async function sendSignInEventToExtension(userId: string, userData: any) {
  await sendAuthEventToExtension(userId, {
    action: 'user_signed_in',
    user: userData
  });
}

/**
 * 向扩展发送登出事件
 */
export async function sendSignOutEventToExtension(userId: string) {
  await sendAuthEventToExtension(userId, {
    action: 'user_signed_out'
  });
}
