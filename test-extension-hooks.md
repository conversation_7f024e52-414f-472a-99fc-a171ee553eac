# Better Auth 扩展通信 Hooks 测试

## 实现说明

我已经为你的 better-auth 配置添加了 hooks，用于在用户登录/登出时自动向扩展发送消息。

### 修改的文件

1. **auth.ts** - 添加了 `hooks.after` 中间件

### 工作原理

1. 当用户通过 better-auth 进行登录或登出操作时
2. better-auth 会触发 `hooks.after` 中间件
3. 中间件检测到是登录/登出相关的路径时，会通过 SSE 发送消息
4. 消息格式与你现有的 `app.vue` 中的格式完全相同：`{ type: 'website_to_ext', action: 'userChanged' }`

### 监听的路径

- `/sign-in/email` - 邮箱登录
- `/sign-in/social` - 社交登录  
- `/verify-otp` - OTP 验证
- `/sign-out` - 登出

### 测试方法

1. 打开浏览器开发者工具的控制台
2. 进行登录操作
3. 查看控制台是否有 "Auth event sent to extension for user xxx" 的日志
4. 检查扩展是否收到了消息

### 扩展端接收

你的扩展应该已经在监听 `window.postMessage` 事件，现在除了原有的用户状态变化外，还会在登录/登出时收到额外的通知。

### 消息流程

```
用户登录/登出 → better-auth hooks → SSE 消息 → 扩展接收
```

这样你就可以在用户登录/登出的第一时间通知扩展，而不需要等待客户端的状态更新。
